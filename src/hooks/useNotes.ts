/**
 * Generic notes hook for unified notes management
 * Supports both client and candidate notes with type safety
 */

import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useUser } from '@/contexts/UserContext';
import { 
  BaseNote, 
  NoteEntityType, 
  ClientNote, 
  CandidateNote,
  CreateClientNoteRequest,
  CreateCandidateNoteRequest,
  UpdateNoteRequest,
  NOTE_CONFIGS
} from '@/types/note';
import { clientNotesApi, candidateNotesApi } from '@/services/notesApi';

/**
 * Generic notes hook configuration
 */
interface UseNotesConfig<T extends BaseNote> {
  entityType: NoteEntityType;
  entityId: string;
  api: {
    getByEntityId: (entityId: string) => Promise<T[]>;
    create: (data: any) => Promise<T>;
    update: (id: string, data: UpdateNoteRequest & { user_id: string }) => Promise<T>;
    delete: (id: string, userId: string) => Promise<void>;
  };
}

/**
 * Generic notes hook return type
 */
interface UseNotesReturn<T extends BaseNote> {
  notes: T[];
  isLoading: boolean;
  isSubmitting: boolean;
  error: Error | null;
  addNote: (content: string) => Promise<T | null>;
  updateNote: (id: string, content: string) => Promise<T | null>;
  deleteNote: (id: string) => Promise<boolean>;
  refreshNotes: () => Promise<void>;
  notesCount: number;
}

/**
 * Generic notes hook
 */
function useNotesGeneric<T extends BaseNote>(config: UseNotesConfig<T>): UseNotesReturn<T> {
  const [notes, setNotes] = useState<T[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();
  const { user } = useUser();

  const noteConfig = NOTE_CONFIGS[config.entityType];

  /**
   * Fetch notes from API
   */
  const fetchNotes = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const fetchedNotes = await config.api.getByEntityId(config.entityId);
      setNotes(fetchedNotes);
    } catch (err) {
      const error = err instanceof Error ? err : new Error(`Failed to fetch ${noteConfig.displayName.toLowerCase()} notes`);
      setError(error);
      console.error(`Error fetching ${noteConfig.displayName.toLowerCase()} notes:`, err);
    } finally {
      setIsLoading(false);
    }
  }, [config.entityId, config.api, noteConfig.displayName]);

  /**
   * Add a new note
   */
  const addNote = useCallback(async (content: string): Promise<T | null> => {
    if (!content.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a note before submitting',
        variant: 'destructive',
      });
      return null;
    }

    try {
      setIsSubmitting(true);
      
      const noteData = {
        [noteConfig.entityIdField]: config.entityId,
        content: content.trim(),
        user_id: user.id,
      };

      const createdNote = await config.api.create(noteData);
      
      // Add the new note to the beginning of the list
      setNotes(prev => [createdNote, ...prev]);

      toast({
        title: 'Success',
        description: `${noteConfig.displayName} note added successfully`,
      });

      return createdNote;
    } catch (err) {
      console.error(`Error creating ${noteConfig.displayName.toLowerCase()} note:`, err);
      toast({
        title: 'Error',
        description: `Failed to add ${noteConfig.displayName.toLowerCase()} note. Please try again.`,
        variant: 'destructive',
      });
      return null;
    } finally {
      setIsSubmitting(false);
    }
  }, [config.entityId, config.api, noteConfig, user.id, toast]);

  /**
   * Update an existing note
   */
  const updateNote = useCallback(async (id: string, content: string): Promise<T | null> => {
    if (!content.trim()) {
      toast({
        title: 'Error',
        description: 'Note content cannot be empty',
        variant: 'destructive',
      });
      return null;
    }

    try {
      const updatedNote = await config.api.update(id, {
        content: content.trim(),
        user_id: user.id,
      });

      // Update the note in the list
      setNotes(prev => prev.map(note => note.id === id ? updatedNote : note));

      toast({
        title: 'Success',
        description: `${noteConfig.displayName} note updated successfully`,
      });

      return updatedNote;
    } catch (err) {
      console.error(`Error updating ${noteConfig.displayName.toLowerCase()} note:`, err);
      toast({
        title: 'Error',
        description: `Failed to update ${noteConfig.displayName.toLowerCase()} note. Please try again.`,
        variant: 'destructive',
      });
      return null;
    }
  }, [config.api, noteConfig, user.id, toast]);

  /**
   * Delete a note
   */
  const deleteNote = useCallback(async (id: string): Promise<boolean> => {
    try {
      await config.api.delete(id, user.id);
      
      // Remove the note from the list
      setNotes(prev => prev.filter(note => note.id !== id));

      toast({
        title: 'Success',
        description: `${noteConfig.displayName} note deleted successfully`,
      });

      return true;
    } catch (err) {
      console.error(`Error deleting ${noteConfig.displayName.toLowerCase()} note:`, err);
      toast({
        title: 'Error',
        description: `Failed to delete ${noteConfig.displayName.toLowerCase()} note. Please try again.`,
        variant: 'destructive',
      });
      return false;
    }
  }, [config.api, noteConfig, user.id, toast]);

  /**
   * Refresh notes
   */
  const refreshNotes = useCallback(async () => {
    await fetchNotes();
  }, [fetchNotes]);

  // Fetch notes on mount and when entityId changes
  useEffect(() => {
    if (config.entityId) {
      fetchNotes();
    }
  }, [config.entityId, fetchNotes]);

  return {
    notes,
    isLoading,
    isSubmitting,
    error,
    addNote,
    updateNote,
    deleteNote,
    refreshNotes,
    notesCount: notes.length,
  };
}

/**
 * Client notes hook
 */
export function useClientNotes(clientId: string): UseNotesReturn<ClientNote> {
  return useNotesGeneric<ClientNote>({
    entityType: 'client',
    entityId: clientId,
    api: clientNotesApi,
  });
}

/**
 * Candidate notes hook (maintains backward compatibility)
 */
export function useCandidateNotes(candidateId: string): UseNotesReturn<CandidateNote> {
  return useNotesGeneric<CandidateNote>({
    entityType: 'candidate',
    entityId: candidateId,
    api: candidateNotesApi,
  });
}
