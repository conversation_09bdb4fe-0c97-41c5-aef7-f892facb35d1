/**
 * API service for candidate notes
 */

import { apiClient } from './apiService';
import { CandidateNote, CreateCandidateNoteRequest, UpdateCandidateNoteRequest } from '@/types/candidateNote';

/**
 * Candidate Notes API service
 */
export const candidateNotesApi = {
  /**
   * Get all notes for a specific candidate
   */
  getByCandidateId: async (candidateId: string): Promise<CandidateNote[]> => {
    try {
      const response = await apiClient.get(`/candidate-notes/candidate/${candidateId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching candidate notes:', error);
      throw error;
    }
  },

  /**
   * Create a new note for a candidate
   */
  create: async (data: CreateCandidateNoteRequest & { user_id: string }): Promise<CandidateNote> => {
    try {
      const response = await apiClient.post('/candidate-notes', data);
      return response.data;
    } catch (error) {
      console.error('Error creating candidate note:', error);
      throw error;
    }
  },

  /**
   * Update an existing note
   */
  update: async (id: string, data: UpdateCandidateNoteRequest & { user_id: string }): Promise<CandidateNote> => {
    try {
      const response = await apiClient.put(`/candidate-notes/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating candidate note:', error);
      throw error;
    }
  },

  /**
   * Delete a note
   */
  delete: async (id: string, userId: string): Promise<void> => {
    try {
      await apiClient.delete(`/candidate-notes/${id}`, {
        data: { user_id: userId }
      });
    } catch (error) {
      console.error('Error deleting candidate note:', error);
      throw error;
    }
  }
};
