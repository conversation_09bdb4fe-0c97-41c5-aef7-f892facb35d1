import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import DashboardLayout from '@/components/layout/DashboardLayout';
import JobFormComponent from '@/components/jobs/JobForm';
import { Job } from '@/types/job';
import { Client } from '@/types/client';
import { jobsApi } from '@/services/apiService';

export default function JobForm() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [job, setJob] = useState<Job | undefined>(undefined);
  const [clients, setClients] = useState<Client[]>([]);
  const [isLoadingJob, setIsLoadingJob] = useState<boolean>(false);
  const [isLoadingClients, setIsLoadingClients] = useState<boolean>(false);

  const isEditMode = !!id;

  // Estado para el guardado
  const [isSaving, setIsSaving] = useState(false);

  // Simulación de carga de datos del trabajo para edición
  useEffect(() => {
    if (isEditMode) {
      setIsLoadingJob(true);
      // Aquí se cargarían los datos del trabajo desde la API
      // Por ahora, simulamos una carga con setTimeout
      setTimeout(() => {
        // Datos de ejemplo
        setJob({
          id: id!,
          title: 'Frontend Developer',
          description: 'We are looking for a skilled frontend developer...',
          status: 'open',
          type: 'full-time',
          location: 'Remote',
          salary_range: '$80,000 - $100,000',
          department: 'Engineering',
          requirements: ['3+ years of React experience', 'Strong TypeScript skills'],
          responsibilities: ['Develop new features', 'Maintain existing codebase'],
          posted_date: new Date().toISOString(),
          applicants_count: 0,
          client_id: 'client-1',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
        setIsLoadingJob(false);
      }, 500);
    }
  }, [id, isEditMode]);

  // Simulación de carga de clientes
  useEffect(() => {
    setIsLoadingClients(true);
    // Aquí se cargarían los clientes desde la API
    // Por ahora, simulamos una carga con setTimeout
    setTimeout(() => {
      // Datos de ejemplo
      setClients([
        {
          id: 'client-1',
          company_name: 'Acme Inc.',
          contact_name: 'John Doe',
          email: '<EMAIL>',
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 'client-2',
          company_name: 'Globex Corporation',
          contact_name: 'Jane Smith',
          email: '<EMAIL>',
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ]);
      setIsLoadingClients(false);
    }, 500);
  }, []);



  // Manejar el envío del formulario
  const handleSubmit = async (data: any) => {
    setIsSaving(true);
    try {
      if (isEditMode && id) {
        // Actualizar trabajo existente
        await jobsApi.update(id, data);
        toast({
          title: 'Success',
          description: 'Job updated successfully',
        });
      } else {
        // Crear nuevo trabajo
        await jobsApi.create(data);
        toast({
          title: 'Success',
          description: 'Job created successfully',
        });
      }

      // Redirigir a la lista de trabajos
      navigate('/jobs');
    } catch (error) {
      console.error('Error saving job:', error);
      toast({
        title: 'Error',
        description: 'Failed to save job. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="flex items-center gap-2 mb-6">
        <Button variant="outline" size="icon" onClick={() => navigate(-1)}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold">
          {isEditMode ? 'Edit Job' : 'Add New Job'}
        </h1>
      </div>

      <JobFormComponent
        job={job}
        clients={clients}
        onSubmit={handleSubmit}
        isLoading={isLoadingJob || isLoadingClients || isSaving}
      />
    </DashboardLayout>
  );
}
