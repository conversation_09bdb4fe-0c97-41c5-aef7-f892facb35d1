import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import DashboardLayout from '@/components/layout/DashboardLayout';
import JobFormComponent from '@/components/jobs/JobForm';
import { Job } from '@/types/job';
import { Client } from '@/types/client';
import { jobsApi, clientsApi } from '@/services/apiService';

export default function JobForm() {
  console.log('JobForm component is loading...');
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [job, setJob] = useState<Job | undefined>(undefined);
  const [clients, setClients] = useState<Client[]>([]);
  const [isLoadingJob, setIsLoadingJob] = useState<boolean>(false);
  const [isLoadingClients, setIsLoadingClients] = useState<boolean>(false);

  const isEditMode = !!id;

  // Estado para el guardado
  const [isSaving, setIsSaving] = useState(false);

  // Load job data for editing
  useEffect(() => {
    const loadJob = async () => {
      if (isEditMode && id) {
        setIsLoadingJob(true);
        try {
          const jobData = await jobsApi.getById(id);
          setJob(jobData);
        } catch (error) {
          console.error('Error loading job:', error);
          toast({
            title: 'Error',
            description: 'Failed to load job data. Please try again.',
            variant: 'destructive',
          });
          navigate('/jobs');
        } finally {
          setIsLoadingJob(false);
        }
      }
    };

    loadJob();
  }, [id, isEditMode, toast, navigate]);

  // Load clients from API
  useEffect(() => {
    const loadClients = async () => {
      setIsLoadingClients(true);
      try {
        const clientsData = await clientsApi.getAll();
        setClients(clientsData);
      } catch (error) {
        console.error('Error loading clients:', error);
        toast({
          title: 'Error',
          description: 'Failed to load clients. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoadingClients(false);
      }
    };

    loadClients();
  }, [toast]);



  // Manejar el envío del formulario
  const handleSubmit = async (data: any) => {
    setIsSaving(true);
    try {
      if (isEditMode && id) {
        // Actualizar trabajo existente
        await jobsApi.update(id, data);
        toast({
          title: 'Success',
          description: 'Job updated successfully',
        });
      } else {
        // Crear nuevo trabajo
        await jobsApi.create(data);
        toast({
          title: 'Success',
          description: 'Job created successfully',
        });
      }

      // Redirigir a la lista de trabajos
      navigate('/jobs');
    } catch (error) {
      console.error('Error saving job:', error);
      toast({
        title: 'Error',
        description: 'Failed to save job. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="flex items-center gap-2 mb-6">
        <Button variant="outline" size="icon" onClick={() => navigate(-1)}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold">
          {isEditMode ? 'Edit Job' : 'Add New Job'}
        </h1>
      </div>

      {/* Debug info */}
      <div className="mb-4 p-4 bg-gray-100 rounded">
        <p>Debug Info:</p>
        <p>Is Edit Mode: {isEditMode ? 'Yes' : 'No'}</p>
        <p>Job ID: {id || 'None'}</p>
        <p>Loading Job: {isLoadingJob ? 'Yes' : 'No'}</p>
        <p>Loading Clients: {isLoadingClients ? 'Yes' : 'No'}</p>
        <p>Clients Count: {clients.length}</p>
        <p>Is Saving: {isSaving ? 'Yes' : 'No'}</p>
      </div>

      <JobFormComponent
        job={job}
        clients={clients}
        onSubmit={handleSubmit}
        isLoading={isLoadingJob || isLoadingClients || isSaving}
      />
    </DashboardLayout>
  );
}
